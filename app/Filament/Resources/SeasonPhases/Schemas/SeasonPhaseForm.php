<?php

namespace App\Filament\Resources\SeasonPhases\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class SeasonPhaseForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('format')
                    ->required(),
                TextInput::make('config')
                    ->required(),
                TextInput::make('teams_count')
                    ->required()
                    ->numeric(),
                TextInput::make('status')
                    ->required(),
                TextInput::make('season_id')
                    ->required()
                    ->numeric(),
            ]);
    }
}
