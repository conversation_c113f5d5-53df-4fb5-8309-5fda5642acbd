<?php

namespace App\Http\Controllers;

use App\Enums\ShirtType;
use App\Models\Competition;
use App\Models\FantasyTeam;
use App\Models\Player;
use App\Models\Tenant;
use App\Services\FantasyContextService;
use App\Services\FantasyValidationService;
use App\Services\PlayerDataService;
use App\Services\SquadManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class FantasyTeamController extends Controller
{
    protected FantasyContextService $contextService;

    protected PlayerDataService $playerDataService;

    protected SquadManagementService $squadService;

    protected FantasyValidationService $validationService;

    public function __construct(
        FantasyContextService $contextService,
        PlayerDataService $playerDataService,
        SquadManagementService $squadService,
        FantasyValidationService $validationService
    ) {
        $this->contextService = $contextService;
        $this->playerDataService = $playerDataService;
        $this->squadService = $squadService;
        $this->validationService = $validationService;
    }

    /**
     * Show the form for creating a new fantasy team.
     */
    public function create()
    {
        $tenant = Tenant::current();
        $user = Auth::user();

        if (! $tenant || ! $user) {
            return redirect()->route('home');
        }

        // Get current competition from session
        $currentCompetitionId = session('current_competition_id');

        if (! $currentCompetitionId) {
            return redirect()->route('home');
        }

        // Get the current competition with its current season
        $competition = Competition::with('currentSeason.teams')
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);
        $CurrentSeasonTeams = [];
        if ($competition->currentSeason) {
            $CurrentSeasonTeams = $competition->currentSeason->teams;
        }
        if (! $competition || ! $competition->currentSeason) {
            return redirect()->route('home');
        }

        // Check if user already has a fantasy team for this season
        $existingTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $competition->currentSeason->id)
            ->first();

        if ($existingTeam) {
            return redirect()->route('home');
        }

        return Inertia::render('FantasyTeam/Create', [
            'competition' => $competition,
            'teams' => $CurrentSeasonTeams,
            'season' => $competition->currentSeason,
            'shirtTypes' => ShirtType::options(),
        ]);
    }

    /**
     * Store a newly created fantasy team in storage.
     */
    public function store(Request $request)
    {
        $tenant = Tenant::current();
        $user = Auth::user();

        if (! $tenant || ! $user) {
            return redirect()->route('home');
        }

        // Get current competition from session
        $currentCompetitionId = session('current_competition_id');

        if (! $currentCompetitionId) {
            return redirect()->route('home');
        }

        // Get the current competition with its current season
        $competition = Competition::with('currentSeason')
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition || ! $competition->currentSeason) {
            return redirect()->route('home');
        }

        // Check if user already has a fantasy team for this season
        $existingTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $competition->currentSeason->id)
            ->first();

        if ($existingTeam) {
            return redirect()->route('home');
        }

        // Validate the request
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'shirt_type' => 'required|in:'.implode(',', ShirtType::values()),
            'shirt_color' => 'nullable|string|max:7',
            'strip_color' => 'nullable|string|max:7',
            'favorite_team' => 'nullable|string|max:255',
        ]);

        // Create the fantasy team and update user in a transaction
        DB::transaction(function () use ($user, $competition, $validated) {
            FantasyTeam::create([
                'user_id' => $user->id,
                'season_id' => $competition->currentSeason->id,
                'name' => $validated['name'],
                'shirt_type' => ShirtType::from($validated['shirt_type']),
                'shirt_color' => $validated['shirt_color'] ?? '#FF0000',
                'strip_color' => $validated['strip_color'] ?? '#FFFFFF',
                'budget' => 100,
                'total_points' => 0,
            ]);

            $user->update(['favorite_team' => $validated['favorite_team'] ?? null]);
        });

        return redirect()->route('fantasy-team.squad')->with('success', 'Fantasy team created successfully! Now set up your squad.');
    }

    /**
     * Show the squad setup form (step 2)
     */
    public function squad()
    {
        $tenant = Tenant::current();
        $user = Auth::user();

        if (! $tenant || ! $user) {
            return redirect()->route('home');
        }

        // Get current competition from session
        $currentCompetitionId = session('current_competition_id');

        if (! $currentCompetitionId) {
            return redirect()->route('home');
        }

        // Get the current competition with its current season and gameweek
        $competition = Competition::with(['currentSeason.currentSeasonPhase.currentGameweek'])
            ->whereHas('tenants', function ($query) use ($tenant) {
                $query->where('tenant_id', $tenant->id);
            })
            ->find($currentCompetitionId);

        if (! $competition || ! $competition->currentSeason) {
            return redirect()->route('home');
        }

        $currentSeason = $competition->currentSeason;
        $currentGameweek = $currentSeason->currentSeasonPhase?->currentGameweek ?? null;

        if (! $currentGameweek) {
            return redirect()->route('home')->with('error', 'No active gameweek found.');
        }

        // Get user's fantasy team
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create');
        }

        // Get current squad if any
        $currentSquad = \App\Models\FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->with(['player', 'fantasyTeamLineups'])
            ->get();

        // Check if user already has a valid squad - if so, redirect to home
        if ($this->hasValidSquad($fantasyTeam, $currentGameweek)) {
            return redirect()->route('home')->with('success', 'Your squad is already set up for this gameweek.');
        }

        return Inertia::render('FantasyTeam/Squad', [
            'competition' => $competition,
            'season' => $currentSeason,
            'gameweek' => $currentGameweek,
            'fantasyTeam' => $fantasyTeam,
            'availablePlayers' => ['data' => [], 'meta' => []], // Empty initial structure for AJAX loading
            'currentSquad' => $currentSquad,
            'budget' => $fantasyTeam->budget, // Use raw budget value from database
        ]);
    }

    /**
     * Get available players for AJAX requests (island architecture)
     */
    public function availablePlayers(Request $request)
    {
        try {

            $tenant = Tenant::current();
            $user = Auth::user();

            if (! $tenant || ! $user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            // Get current competition from session
            $currentCompetitionId = session('current_competition_id');

            if (! $currentCompetitionId) {
                return response()->json(['error' => 'No competition selected'], 400);
            }

            // Get the current competition with its current season
            $competition = Competition::with(['currentSeason'])
                ->whereHas('tenants', function ($query) use ($tenant) {
                    $query->where('tenant_id', $tenant->id);
                })
                ->find($currentCompetitionId);

            if (! $competition || ! $competition->currentSeason) {
                return response()->json(['error' => 'Competition not found'], 404);
            }

            $currentSeason = $competition->currentSeason;

            // Get user's fantasy team
            $fantasyTeam = FantasyTeam::where('user_id', $user->id)
                ->where('season_id', $currentSeason->id)
                ->first();

            if (! $fantasyTeam) {
                return response()->json(['error' => 'Fantasy team not found'], 404);
            }

            // Set locale for translations
            if ($request->has('locale')) {
                app()->setLocale($request->get('locale'));
            }

            // Get filters from request
            $position = $request->get('position', 'all');
            $team = $request->get('team', 'all');
            $sort = $request->get('sort', 'name_asc');
            $maxPrice = $request->get('maxPrice');

            // Build query for available players
            $query = \App\Models\Player::whereHas('teams', function ($teamQuery) use ($currentSeason) {
                $teamQuery->whereHas('seasons', function ($seasonQuery) use ($currentSeason) {
                    $seasonQuery->where('seasons.id', $currentSeason->id);
                });
            })
                ->with(['teams', 'marketValues' => function ($query) {
                    $query->latest('created_at')->limit(1);
                }]);

            // Apply position filter
            if ($position !== 'all') {
                $query->where('position', $position);
            }

            // Apply team filter
            if ($team !== 'all') {
                $query->whereHas('teams', function ($teamQuery) use ($team) {
                    $teamQuery->where('name', $team);
                });
            }

            // Apply price filter
            if ($maxPrice) {
                $maxPriceInCents = $maxPrice * 1000000; // Convert to cents
                $query->whereHas('marketValues', function ($marketQuery) use ($maxPriceInCents) {
                    $marketQuery->where('market_value', '<=', $maxPriceInCents);
                });
            }

            // Apply sorting (only name sorting can be done at DB level)
            if ($sort === 'name_asc') {
                $query->orderBy('name', 'asc');
            } elseif ($sort === 'name_desc') {
                $query->orderBy('name', 'desc');
            }

            // Use Laravel's built-in pagination

            $players = $query->paginate(20);

            // Transform the paginated data
            $players->getCollection()->transform(function ($player) {

                // Use eager-loaded marketValues to avoid N+1 queries
                $marketValue = $player->marketValues->first()?->market_value ?? 5000000; // Default 5M if no market value

                return [
                    'id' => $player->id,
                    'name' => $player->name,
                    'position' => $player->position,
                    'team' => $player->teams->first()?->name ?? 'Unknown',
                    'price' => $marketValue / 1000000, // Convert to millions
                    'market_value' => $marketValue,
                ];
            });

            // For price sorting, we need to handle it after pagination due to relationship complexity
            if (in_array($sort, ['price_asc', 'price_desc'])) {
                $collection = $players->getCollection();
                if ($sort === 'price_asc') {
                    $collection = $collection->sortBy('market_value');
                } else {
                    $collection = $collection->sortByDesc('market_value');
                }
                $players->setCollection($collection->values());
            }

            return response()->json($players);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Internal server error',
                'message' => $e->getMessage(),
                'debug' => config('app.debug') ? $e->getTraceAsString() : null,
            ], 500);
        }
    }

    /**
     * Store the squad setup (step 2)
     */
    public function storeSquad(Request $request)
    {
        $user = Auth::user();
        if (! $user) {
            return redirect()->route('home');
        }

        // Get fantasy context using service
        $contextResult = $this->contextService->getFantasyContextWithErrors($request);
        if (! $contextResult['success']) {
            return redirect()->route('home')->with('error', $contextResult['error']);
        }

        $context = $contextResult['data'];
        $currentGameweek = $context['currentGameweek'];
        $currentSeason = $context['currentSeason'];

        // Get user's fantasy team
        $fantasyTeam = FantasyTeam::where('user_id', $user->id)
            ->where('season_id', $currentSeason->id)
            ->first();

        if (! $fantasyTeam) {
            return redirect()->route('fantasy-team.create');
        }

        // Validate the squad selection
        $validated = $request->validate([
            'selected_players' => 'required|array|size:15',
            'selected_players.*' => 'required|integer|exists:players,id',
            'lineup_players' => 'required|array|size:11',
            'lineup_players.*' => 'required|integer|exists:players,id',
            'captain_id' => 'required|integer|exists:players,id',
            'vice_captain_id' => 'required|integer|exists:players,id',
        ]);

        // Validate squad composition using service
        $compositionValidation = $this->validationService->validateSquadComposition($validated['selected_players']);
        if (! $compositionValidation['valid']) {
            return redirect()->back()->withErrors([
                'squad' => implode(' ', $compositionValidation['errors']),
            ]);
        }

        // Skip lineup and captain validation for initial squad creation
        // The service will automatically assign 4-4-2 formation and handle captain validation
        // This is different from transfers where user selects their own formation

        // Calculate player market values and validate budget
        $allPlayers = Player::whereIn('id', $validated['selected_players'])->get()->keyBy('id');
        $playerMarketValues = [];

        foreach ($validated['selected_players'] as $playerId) {
            $player = $allPlayers[$playerId];
            $marketValue = $player->getMarketValueForGameweek($currentGameweek->id) ?? 5; // Default 5 if no market value
            $playerMarketValues[$playerId] = $marketValue;
        }

        // Validate budget using service
        $budgetValidation = $this->validationService->validateBudget(
            $validated['selected_players'],
            $playerMarketValues,
            $fantasyTeam->budget
        );

        if (! $budgetValidation['valid']) {
            return redirect()->back()->withErrors([
                'budget' => 'Squad cost ('.number_format($budgetValidation['totalCost'], 1).') exceeds your budget ('.number_format($budgetValidation['availableBudget'], 1).')',
            ]);
        }

        // Clear existing squad for this gameweek
        \App\Models\FantasyPlayer::where('fantasy_team_id', $fantasyTeam->id)
            ->where('gameweek_id', $currentGameweek->id)
            ->delete();

        // Update fantasy team budget after squad creation
        $remainingBudget = $fantasyTeam->budget - $budgetValidation['totalCost'];
        $fantasyTeam->updateBudget($remainingBudget);

        // Use squad management service to create initial squad with lineup
        $this->squadService->createInitialSquad(
            $fantasyTeam,
            $currentGameweek,
            $validated,
            $playerMarketValues
        );

        return redirect()->route('home')->with('success', 'Squad setup completed successfully!');
    }

    /**
     * Check if the fantasy team has a valid squad for the given gameweek
     */
    private function hasValidSquad(FantasyTeam $fantasyTeam, $gameweek)
    {
        return $this->validationService->hasValidSquad($fantasyTeam, $gameweek);
    }
}
