<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()
            ->count(1)
            ->create([
                'email' => '<EMAIL>',
            ]);

        $this->call(TenantSeeder::class);
        $this->call(SubscriptionSeeder::class);
        $this->call(PaymentSeeder::class);
        $this->call(CompetitionSeeder::class);
        $this->call(SeasonSeeder::class);
        $this->call(TeamSeeder::class);
        $this->call(PlayerSeeder::class);
        $this->call(SeasonPhaseSeeder::class);
        $this->call(GameweekSeeder::class);
        $this->call(FantasyTeamSeeder::class);
        $this->call(FantasyPlayerSeeder::class);
        $this->call(FantasyTeamLineupSeeder::class);
        $this->call(FantasyTransferSeeder::class);
        $this->call(GameSeeder::class);
        $this->call(PlayerPerformanceSeeder::class);
        $this->call(FantasyPointSeeder::class);
        $this->call(LeagueSeeder::class);
        $this->call(RankingGlobalSeeder::class);
        $this->call(RankingFavoriteTeamSeeder::class);
        $this->call(RankingJoinedGameweekSeeder::class);
        $this->call(RankingLeagueSeeder::class);
        $this->call(BoosterSeeder::class);
    }
}
