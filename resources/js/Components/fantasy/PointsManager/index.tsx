import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { PointsManagerProps, Player } from "@/types/fantasy";
import {
    initializeTeam,
    applyFormation,
    getFormationKey,
} from "@/lib/fantasy/team-management";

import Pitch from "../common/Pitch";
import Bench from "../common/Bench";

export default function PointsManager({
    squadPlayers,
    startingPlayerIds,
    fantasyTeam,
}: PointsManagerProps) {
    const { t } = useTranslation();

    const initialSquad = useMemo(
        () =>
            squadPlayers.map((player) => ({
                id: player.id,
                name: player.name,
                position: player.position,
                price: player.price, // price is not displayed but good to have
                points: player.points,
                is_captain: false, // not relevant for points view
                is_vice_captain: false, // not relevant for points view
                onPitch: false,
                pitchPosition: null,
                team_data: player.team_data,
            })),
        [squadPlayers]
    );

    const players = useMemo(
        () => initializeTeam(initialSquad, startingPlayerIds, t),
        [initialSquad, startingPlayerIds, t]
    );

    return (
        <div className="flex gap-4">
            <div className="transparent-card min-h-screen rounded-lg p-4 sm:p-8 font-sans pb-24 w-full lg:w-2/3">
                <div className="max-w-5xl mx-auto">
                    <Pitch players={players} />
                    <Bench players={players} />
                </div>
            </div>
            <div className="transparent-card min-h-screen rounded-lg p-4 sm:p-8 font-sans pb-24 w-full lg:w-1/3">
                <h3 className="text-center">GameWeek</h3>
                {/* GameWeek will be here */}
            </div>
        </div>
    );
}
