import React, { FC } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";
import PlayerComponent from "./PlayerComponent";
import { squadLayout } from "@/lib/fantasy/transfer-management";

const SquadPitch: FC<{
    players: TransferPlayer[];
    onPlayerClick: (player: TransferPlayer, element: HTMLElement) => void;
    onPlayerInfoClick: (player: TransferPlayer) => void;
    selectedPlayerId: number | string | null;
}> = ({ players, onPlayerClick, onPlayerInfoClick, selectedPlayerId }) => {
    const { t } = useTranslation();
    const positionCounters = { GK: 0, DEF: 0, MID: 0, FWD: 0 };
    return (
        <div className="transparent-card p-4 rounded-lg">
            <h2 className="text-white text-xl font-bold mb-4 text-center">
                {t("transferManager.titles.squad")}
            </h2>
            <div className="fantasy-pitch">
                {players.map((player) => {
                    const posType = player.position;
                    const posIndex = positionCounters[posType];
                    const pitchPosition = squadLayout[posType][posIndex];
                    positionCounters[posType]++;
                    return (
                        <PlayerComponent
                            key={player.id}
                            player={player}
                            position={pitchPosition}
                            onClick={onPlayerClick}
                            onInfoClick={onPlayerInfoClick}
                            isSelected={player.id === selectedPlayerId}
                        />
                    );
                })}
            </div>
        </div>
    );
};

export default SquadPitch;
