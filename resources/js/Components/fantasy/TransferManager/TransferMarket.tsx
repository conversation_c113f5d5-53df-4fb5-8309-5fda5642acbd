import React, { FC, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { TransferPlayer } from "@/types/fantasy";
import MarketPlayer from "./MarketPlayer";
import { validateTransfer } from "@/lib/fantasy/transfer-management";
import { Paginated } from "@/types/pagination";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/Components/ui/select";

interface TransferFilters {
    position: string;
    team: string;
    sort: string;
    maxPrice: number | null;
    page?: number;
}

const TransferMarket: FC<{
    paginatedPlayers: Paginated<TransferPlayer>;
    onPlayerClick: (player: TransferPlayer) => void;
    onPlayerInfoClick: (player: TransferPlayer) => void;
    isCreationMode?: boolean;
    selectedPlayerId: number | string | null;
    squad: TransferPlayer[];
    balance: number;
    filters: TransferFilters;
    setFilters: React.Dispatch<React.SetStateAction<TransferFilters>>;
    onFetchPlayers?: (filters?: any) => Promise<void>;
    isLoadingPlayers?: boolean;
}> = ({
    paginatedPlayers,
    onPlayerClick,
    onPlayerInfoClick,
    selectedPlayerId,
    squad,
    balance,
    filters,
    setFilters,
    isCreationMode,
    onFetchPlayers,
    isLoadingPlayers = false,
}) => {
    const { t } = useTranslation();
    const { data: players = [] } = paginatedPlayers || {
        data: [],
    };

    const squadIds = useMemo(() => new Set(squad.map((p) => p.id)), [squad]);

    const teams = useMemo(
        () => [...new Set(players.map((p) => p.team))].sort(),
        [players]
    );

    const [isLoading, setIsLoading] = React.useState(false);
    
    // Use external loading state if provided, otherwise use internal state
    const currentlyLoading = isLoadingPlayers || isLoading;

    const handleFilterChange = (newFilters: Partial<TransferFilters>) => {
        const updatedFilters = { ...filters, ...newFilters, page: 1 };
        setFilters(updatedFilters);
        if (onFetchPlayers) {
            onFetchPlayers(updatedFilters);
        }
    };

    const handlePageChange = (url: string | null) => {
        if (url && onFetchPlayers) {
            setIsLoading(true);
            const page = new URL(url).searchParams.get("page");
            const newFilters = { ...filters, page: page ? parseInt(page, 10) : 1 };
            setFilters(newFilters);
            onFetchPlayers(newFilters);
        }
    };

    useEffect(() => {
        setIsLoading(false);
    }, [paginatedPlayers]);

    return (
        <div className="transparent-card p-4 rounded-lg w-full lg:w-1/2">
            <h2 className="text-white text-xl font-bold mb-4 text-center">
                {t("transferManager.titles.transferMarket")}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-xs">
                <Select
                    value={filters.position}
                    onValueChange={(value) =>
                        handleFilterChange({
                            position: value,
                        })
                    }
                >
                    <SelectTrigger className="bg-gray-700 text-white border-gray-600">
                        <SelectValue placeholder={t("transferManager.filters.allPositions")} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">
                            {t("transferManager.filters.allPositions")}
                        </SelectItem>
                        <SelectItem value="GK">GK</SelectItem>
                        <SelectItem value="DEF">DEF</SelectItem>
                        <SelectItem value="MID">MID</SelectItem>
                        <SelectItem value="FWD">FWD</SelectItem>
                    </SelectContent>
                </Select>
                <Select
                    value={filters.team}
                    onValueChange={(value) =>
                        handleFilterChange({
                            team: value,
                        })
                    }
                >
                    <SelectTrigger className="bg-gray-700 text-white border-gray-600">
                        <SelectValue placeholder={t("transferManager.filters.allTeams")} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">
                            {t("transferManager.filters.allTeams")}
                        </SelectItem>
                        {teams.map((team) => (
                            <SelectItem key={team} value={team}>
                                {team}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <input
                    type="number"
                    placeholder={t("transferManager.filters.maxPrice")}
                    value={filters.maxPrice ?? ""}
                    onChange={(e) =>
                        handleFilterChange({
                            maxPrice: e.target.value
                                ? parseFloat(e.target.value)
                                : null,
                        })
                    }
                    className="bg-gray-700 text-white p-2 rounded-md"
                />
                <button
                    onClick={() =>
                        handleFilterChange({
                            position: "all",
                            team: "all",
                            sort: "price_desc",
                            maxPrice: null,
                        })
                    }
                    className="bg-red-500 text-white p-2 rounded-md"
                >
                    {t("transferManager.actions.resetFilters")}
                </button>
            </div>
            <div className="overflow-y-auto h-fit">
                <table className="w-full text-left text-white text-xs">
                    <thead className="sticky top-0 bg-gray-900">
                        <tr>
                            <th className="p-2"></th>
                            <th className="p-2">
                                {t("transferManager.market.player")}
                            </th>
                            <th className="p-2">
                                {t("transferManager.market.team")}
                            </th>
                            <th className="p-2">
                                {t("transferManager.market.pos")}
                            </th>
                            <th className="p-2 text-right">
                                {t("transferManager.market.price")}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {players.map((p) => {
                            const isInSquad = squadIds.has(p.id);
                            const selectedSquadPlayer = squad.find(
                                (sp) => sp.id === selectedPlayerId
                            );
                            const isEligible = selectedSquadPlayer
                                ? validateTransfer(
                                      selectedSquadPlayer,
                                      p,
                                      squad,
                                      balance,
                                      t
                                  ).valid
                                : false;
                            return (
                                <MarketPlayer
                                    key={p.id}
                                    player={p}
                                    onPlayerClick={onPlayerClick}
                                    onPlayerInfoClick={onPlayerInfoClick}
                                    isEligible={isEligible}
                                    isInSquad={isInSquad}
                                />
                            );
                        })}
                    </tbody>
                </table>
            </div>
            <div className="flex justify-between items-center mt-4 text-xs">
                <button
                    onClick={() =>
                        handlePageChange(paginatedPlayers.prev_page_url)
                    }
                    disabled={!paginatedPlayers.prev_page_url || currentlyLoading}
                    className={`bg-gray-600 cursor-pointer text-white font-bold py-2 px-4 rounded disabled:cursor-not-allowed disabled:opacity-50 ${
                        currentlyLoading && "opacity-50"
                    }`}
                >
                    {currentlyLoading ? "..." : t("pagination.previous")}
                </button>
                <span className="text-white">
                    {t("pagination.page", {
                        current: paginatedPlayers?.current_page ?? 1,
                        total: paginatedPlayers?.last_page ?? 1,
                    })}
                </span>
                <button
                    onClick={() =>
                        handlePageChange(paginatedPlayers.next_page_url)
                    }
                    disabled={!paginatedPlayers.next_page_url || currentlyLoading}
                    className={`bg-gray-600 cursor-pointer text-white font-bold py-2 px-4 rounded disabled:cursor-not-allowed disabled:opacity-50 ${
                        currentlyLoading && "opacity-50"
                    }`}
                >
                    {currentlyLoading ? "..." : t("pagination.next")}
                </button>
            </div>
        </div>
    );
};

export default TransferMarket;
