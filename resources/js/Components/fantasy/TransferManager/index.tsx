import React, { useState, useMemo, useEffect } from "react";
import { router } from "@inertiajs/react";
import { useTranslation } from "react-i18next";
import { TransferPlayer, TransferManagerProps } from "@/types/fantasy";
import {
    createPlaceholderSquad,
    calculateSquadValue,
    validateTransfer,
    POINTS_PER_TRANSFER,
} from "@/lib/fantasy/transfer-management";
import PlayerInfoModal from "../common/PlayerInfoModal";
import SquadPitch from "./SquadPitch";
import TransferMarket from "./TransferMarket";
import BudgetDisplay from "./BudgetDisplay";
import CaptainSelection from "./CaptainSelection";
import ConfirmationModal from "./ConfirmationModal";
import ActionMenu from "./ActionMenu";
import MessageBox from "../common/MessageBox";

interface TransferFilters {
    position: string;
    team: string;
    sort: string;
    maxPrice: number | null;
    page?: number;
}

function TransferManager({
    isCreationMode = false,
    initialSquadData = [],
    availablePlayers,
    initialBudget = 100.0,
    onSquadSubmit,
    onFetchPlayers,
    isLoadingPlayers = false,
}: TransferManagerProps) {
    const { t } = useTranslation();
    const [initialSquadState, setInitialSquadState] = useState<
        TransferPlayer[]
    >(() =>
        isCreationMode ? createPlaceholderSquad(t) : [...initialSquadData]
    );
    const [squad, setSquad] = useState<TransferPlayer[]>(
        isCreationMode ? createPlaceholderSquad(t) : initialSquadData
    );
    const [balance, setBalance] = useState<number>(
        isCreationMode ? initialBudget : initialBudget // Use the budget value directly from backend
    );
    const [freeTransfers, setFreeTransfers] = useState(0);
    const [initialFreeTransfers, setInitialFreeTransfers] = useState(0);
    const [selectedSquadPlayerId, setSelectedSquadPlayerId] = useState<
        number | string | null
    >(null);
    const [message, setMessage] = useState<{
        text: string;
        type: "info" | "error";
    }>({
        text: isCreationMode
            ? t("transferManager.selectPlaceholderToAddPlayer")
            : t("transferManager.selectPlayerToTransferOut"),
        type: "info",
    });
    const [filters, setFilters] = useState<TransferFilters>(() => {
        const params = new URLSearchParams(window.location.search);
        const pageParam = params.get("page");
        const maxPriceParam = params.get("maxPrice");

        return {
            position: params.get("position") || "all",
            team: params.get("team") || "all",
            sort: params.get("sort") || "price_desc",
            maxPrice:
                maxPriceParam !== null && maxPriceParam !== ""
                    ? parseFloat(maxPriceParam)
                    : null,
            page: pageParam ? parseInt(pageParam, 10) : 1,
        };
    });

    useEffect(() => {
        if (onFetchPlayers) {
            const query = { ...filters };

            // Remove null, undefined, or empty string values from the query object
            Object.keys(query).forEach((key) => {
                const typedKey = key as keyof TransferFilters;
                if (
                    query[typedKey] === null ||
                    query[typedKey] === undefined ||
                    query[typedKey] === ""
                ) {
                    delete query[typedKey];
                }
            });

            onFetchPlayers(query);
        }
    }, [filters, onFetchPlayers]);

    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [modalPlayer, setModalPlayer] = useState<TransferPlayer | null>(null);
    const [captain, setCaptain] = useState<TransferPlayer | null>(null);
    const [viceCaptain, setViceCaptain] = useState<TransferPlayer | null>(null);
    const [actionMenu, setActionMenu] = useState<{
        player: TransferPlayer;
        position: { top: number; left: number };
    } | null>(null);

    const hasChanges = useMemo(
        () => JSON.stringify(initialSquadState) !== JSON.stringify(squad),
        [initialSquadState, squad]
    );
    const isSquadComplete = useMemo(
        () => isCreationMode && squad.every((p) => typeof p.id === "number"),
        [squad, isCreationMode]
    );

    const getChanges = () => {
        const playersIn = squad.filter(
            (p) => !initialSquadState.find((ip) => ip.id === p.id)
        );
        const playersOut = initialSquadState.filter(
            (isp) => !squad.find((p) => p.id === isp.id)
        );
        return { playersIn, playersOut, captain, viceCaptain };
    };

    const handleSquadPlayerClick = (
        clickedPlayer: TransferPlayer,
        element: HTMLElement
    ) => {
        if (isCreationMode) {
            setSelectedSquadPlayerId(clickedPlayer.id);
            setMessage({
                text: t("transferManager.messages.selectPlayerToTransfer", {
                    name: clickedPlayer.name,
                }),
                type: "info",
            });
        } else {
            const rect = element.getBoundingClientRect();
            const parentRect = element
                .closest(".max-w-7xl")
                ?.getBoundingClientRect();
            const parentTop = parentRect?.top ?? 0;
            const parentLeft = parentRect?.left ?? 0;

            setActionMenu({
                player: clickedPlayer,
                position: {
                    top: rect.bottom - parentTop,
                    left: rect.left - parentLeft,
                },
            });
        }
    };

    const handleMarketPlayerClick = (playerIn: TransferPlayer) => {
        const playerOut = squad.find((p) => p.id === selectedSquadPlayerId);
        if (!playerOut) return;

        const validation = validateTransfer(
            playerOut,
            playerIn,
            squad,
            balance,
            t
        );
        if (!validation.valid) {
            setMessage({ text: validation.message, type: "error" });
            return;
        }

        const priceDifference = playerIn.price - playerOut.price;
        setBalance((prev) => prev - priceDifference);

        setSquad((currentSquad) =>
            currentSquad.map((p) => (p.id === playerOut.id ? playerIn : p))
        );

        setMessage({
            text: t("transferManager.messages.playerAdded", {
                name: playerIn.name,
            }),
            type: "info",
        });
        setSelectedSquadPlayerId(null);
    };

    const handleInitiateTransfer = (player: TransferPlayer) => {
        setSelectedSquadPlayerId(player.id);
        setMessage({
            text: t("transferManager.messages.selectPlayerToTransfer", {
                name: player.name,
            }),
            type: "info",
        });
        setActionMenu(null);
    };

    const handlePlayerInfoFromMenu = (player: TransferPlayer) => {
        setModalPlayer(player);
        setActionMenu(null);
    };

    const handleResetClick = () => {
        setSquad(
            isCreationMode
                ? createPlaceholderSquad(t)
                : JSON.parse(JSON.stringify(initialSquadState))
        );
        setBalance(
            isCreationMode ? initialBudget : initialBudget // Use the budget value directly from backend
        );
        if (!isCreationMode) setFreeTransfers(initialFreeTransfers);
        setSelectedSquadPlayerId(null);
        setMessage({
            text: t("transferManager.messages.changesReset"),
            type: "info",
        });
    };

    const handleConfirmTransfers = () => {
        if (isCreationMode) {
            // In creation mode, directly call the final confirm to submit the squad
            handleFinalConfirm();
        } else {
            setShowConfirmationModal(true);
        }
    };

    const handleFinalConfirm = () => {
        const changes = getChanges();
        const transfersMade = changes.playersIn.length;

        if (isCreationMode && onSquadSubmit) {
            // For creation mode, submit the complete squad data
            const realPlayers = squad.filter((p) => typeof p.id === "number");

            onSquadSubmit({
                players: realPlayers,
                captain,
                viceCaptain,
                budget: balance,
            });
        } else if (!isCreationMode && onSquadSubmit) {
            // For transfer mode, submit the transfer data to backend
            const realPlayers = squad.filter((p) => typeof p.id === "number");

            onSquadSubmit({
                players: realPlayers,
                captain,
                viceCaptain,
                budget: balance,
            });

            // Update local state after successful submission
            setInitialSquadState(JSON.parse(JSON.stringify(squad)));
            const newFreeTransfers = Math.max(0, freeTransfers - transfersMade);
            setFreeTransfers(newFreeTransfers);
            setInitialFreeTransfers(newFreeTransfers);
        } else {
            // Fallback for when no onSquadSubmit callback is provided
            setInitialSquadState(JSON.parse(JSON.stringify(squad)));
            const newFreeTransfers = Math.max(0, freeTransfers - transfersMade);
            setFreeTransfers(newFreeTransfers);
            setInitialFreeTransfers(newFreeTransfers);
        }

        setShowConfirmationModal(false);
        setMessage({
            text: isCreationMode
                ? t("transferManager.messages.squadCreated")
                : t("transferManager.messages.transfersConfirmed"),
            type: "info",
        });
    };

    const handlePlayerInfoClick = (player: TransferPlayer) => {
        setModalPlayer(player);
    };

    const handleCaptainSelect = (player: TransferPlayer) => {
        // If selecting the same player as captain, deselect
        if (captain?.id === player.id) {
            setCaptain(null);
            return;
        }

        // If this player is currently vice-captain, remove them from vice-captain
        if (viceCaptain?.id === player.id) {
            setViceCaptain(null);
        }

        setCaptain(player);
    };

    const handleViceCaptainSelect = (player: TransferPlayer) => {
        // If selecting the same player as vice-captain, deselect
        if (viceCaptain?.id === player.id) {
            setViceCaptain(null);
            return;
        }

        // If this player is currently captain, remove them from captain
        if (captain?.id === player.id) {
            setCaptain(null);
        }

        setViceCaptain(player);
    };

    return (
        <div>
            <BudgetDisplay
                squadValue={calculateSquadValue(
                    squad.filter((p) => typeof p.id === "number")
                )}
                balance={balance}
                freeTransfers={freeTransfers}
                isCreationMode={isCreationMode}
            />
            <MessageBox message={message.text} type={message.type} />

            {/* Captain/Vice-Captain Selection - only show in creation mode when squad is complete */}
            {isCreationMode && isSquadComplete && (
                <CaptainSelection
                    squad={squad}
                    captain={captain}
                    viceCaptain={viceCaptain}
                    onCaptainSelect={handleCaptainSelect}
                    onViceCaptainSelect={handleViceCaptainSelect}
                />
            )}

            <div className="flex flex-col lg:flex-row gap-4">
                <div className="relative w-full lg:w-2/3">
                    {actionMenu && (
                        <ActionMenu
                            player={actionMenu.player}
                            position={actionMenu.position}
                            onClose={() => setActionMenu(null)}
                            onInitiateTransfer={handleInitiateTransfer}
                            onPlayerInfo={handlePlayerInfoFromMenu}
                        />
                    )}
                    <SquadPitch
                        players={squad}
                        onPlayerClick={handleSquadPlayerClick}
                        onPlayerInfoClick={handlePlayerInfoClick}
                        selectedPlayerId={selectedSquadPlayerId}
                    />
                </div>
                <TransferMarket
                    paginatedPlayers={availablePlayers}
                    onPlayerClick={handleMarketPlayerClick}
                    onPlayerInfoClick={handlePlayerInfoClick}
                    selectedPlayerId={selectedSquadPlayerId}
                    isCreationMode={isCreationMode}
                    squad={squad}
                    balance={balance}
                    filters={filters}
                    setFilters={setFilters}
                    onFetchPlayers={onFetchPlayers}
                    isLoadingPlayers={isLoadingPlayers}
                />
            </div>
            {(hasChanges || (isCreationMode && isSquadComplete)) && (
                <div className="fixed bottom-0 left-0 right-0 z-50 bg-gray-800/50 backdrop-blur supports-[backdrop-filter]:bg-gray-800/30 p-4 flex justify-center space-x-4">
                    <button
                        onClick={handleResetClick}
                        className="bg-red-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-red-700 transition-colors"
                    >
                        Reset
                    </button>
                    <button
                        onClick={handleConfirmTransfers}
                        disabled={
                            isCreationMode &&
                            (!isSquadComplete || !captain || !viceCaptain)
                        }
                        className="bg-green-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-500 disabled:cursor-not-allowed"
                    >
                        {isCreationMode
                            ? t("transferManager.actions.createTeam")
                            : t("transferManager.actions.confirmTransfers")}
                    </button>
                </div>
            )}
            {showConfirmationModal && (
                <ConfirmationModal
                    changes={getChanges()}
                    cost={
                        Math.max(
                            0,
                            getChanges().playersIn.length - freeTransfers
                        ) * POINTS_PER_TRANSFER
                    }
                    onConfirm={handleFinalConfirm}
                    onCancel={() => setShowConfirmationModal(false)}
                />
            )}
            <PlayerInfoModal
                player={modalPlayer}
                onClose={() => setModalPlayer(null)}
                context="transferManager"
            />
        </div>
    );
}

export default TransferManager;
