import { Head, router } from "@inertiajs/react";
import Layout from "../../Components/Layout";
import { PageProps } from "../../types/inertia";
import HeroSection from "../../Components/ui/HeroSection";
import { useTenantName } from "@/hooks/useTenant";
import TransferManager from "@/Components/fantasy/TransferManager";
import { useTranslation } from "react-i18next";
import { useState, useCallback } from 'react';
import axios from "axios";
import { Paginated } from "@/types/pagination";

interface Player {
    id: number | string;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

interface SquadPageProps extends PageProps {
    competition: any;
    season: any;
    gameweek: any;
    fantasyTeam: any;
    availablePlayers: Paginated<any>;
    currentSquad: any[];
    budget: number;
}

export default function Squad(props: SquadPageProps) {
    const tenantName = useTenantName();
    const { t, i18n } = useTranslation();
    const { currentSquad, budget } = props;

    console.log('Squad component mounted with props:', props);

    // AJAX state for available players
    const [availablePlayers, setAvailablePlayers] = useState<Paginated<any>>({
        data: [],
        current_page: 1,
        first_page_url: '',
        from: 0,
        last_page: 1,
        last_page_url: '',
        links: [],
        next_page_url: null,
        path: '',
        per_page: 20,
        prev_page_url: null,
        to: 0,
        total: 0,
    });
    const [isLoadingPlayers, setIsLoadingPlayers] = useState(false);

    // Function to fetch available players via AJAX
    const fetchAvailablePlayers = useCallback(async (filters: any = {}) => {
        console.log('fetchAvailablePlayers called with filters:', filters);
        setIsLoadingPlayers(true);
        try {
            console.log('Making AJAX request to /fantasy-team/available-players');
            const response = await axios.get('/fantasy-team/available-players', {
                params: {
                    ...filters,
                    locale: i18n.language,
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });
            console.log('AJAX response received:', response.data);
            setAvailablePlayers(response.data);
        } catch (error: any) {
            console.error('Failed to fetch available players:', error);
            console.error('Error details:', error.response?.data);
        } finally {
            setIsLoadingPlayers(false);
        }
    }, [i18n.language]);

    console.log('available  ', availablePlayers);
    // Transform backend data to match TransferManager interface
    const transformedAvailablePlayers: Player[] = (availablePlayers.data || []).map(
        (player: any) => ({
            id: player.id,
            name: player.name,
            position: player.position as "GK" | "DEF" | "MID" | "FWD",
            team: player.team || t("fantasyTeam.squad.unknown"),
            price: player.price || 5, // Already converted to millions in backend
        })
    );

    console.log('transformed available players ', transformedAvailablePlayers);
    // Transform current squad data if exists
    const transformedCurrentSquad: Player[] = currentSquad.map(
        (fantasyPlayer: any) => ({
            id: fantasyPlayer.player.id,
            name: fantasyPlayer.player.name,
            position: fantasyPlayer.player.position as
                | "GK"
                | "DEF"
                | "MID"
                | "FWD",
            team: fantasyPlayer.player.teams?.[0]?.name || t("fantasyTeam.squad.unknown"),
            price: (fantasyPlayer.player.market_value || 5000000) / 1000000, // Convert to millions for display
            is_captain:
                fantasyPlayer.fantasyTeamLineups?.some(
                    (lineup: any) => lineup.is_captain
                ) || false,
            is_vice_captain:
                fantasyPlayer.fantasyTeamLineups?.some(
                    (lineup: any) => lineup.is_vice_captain
                ) || false,
        })
    );

    const handleSquadSubmit = (squadData: {
        players: Player[];
        captain: Player | null;
        viceCaptain: Player | null;
        budget: number;
    }) => {
        // Transform data for backend submission to match controller expectations
        const selectedPlayers = squadData.players.map((player) => player.id);

        // For now, assume first 11 players are in lineup (this should be enhanced later)
        const lineupPlayers = squadData.players
            .slice(0, 11)
            .map((player) => player.id);

        const submissionData = {
            selected_players: selectedPlayers,
            lineup_players: lineupPlayers,
            captain_id: squadData.captain?.id || null,
            vice_captain_id: squadData.viceCaptain?.id || null,
        };
        // Submit to backend
        router.post(route("fantasy-team.squad.store"), submissionData, {
            onSuccess: () => {
                // Redirect to home or next step after successful squad creation
                router.visit(route("home"));
            },
            onError: (errors) => {
                console.error("Squad submission failed:", errors);
            },
        });
    };

    return (
        <Layout {...props}>
            <Head title={`${tenantName} - ${t("fantasyTeam.squad.squad")}`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title={t("fantasyTeam.squad.createYourSquad")}
                    subtitle={t(
                        "fantasyTeam.squad.buildYourDreamTeam"
                    )}
                />
            </div>
            <TransferManager
                isCreationMode={true}
                initialSquadData={transformedCurrentSquad}
                availablePlayers={availablePlayers}
                initialBudget={budget}
                onSquadSubmit={handleSquadSubmit}
                onFetchPlayers={fetchAvailablePlayers}
                isLoadingPlayers={isLoadingPlayers}
            />
        </Layout>
    );
}
