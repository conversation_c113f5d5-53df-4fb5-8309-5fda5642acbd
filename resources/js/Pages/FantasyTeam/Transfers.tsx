import { Head, router } from "@inertiajs/react";
import Layout from "../../Components/Layout";
import { PageProps } from "../../types/inertia";
import HeroSection from "@/Components/ui/HeroSection";

import { useTenantName } from "@/hooks/useTenant";
import TransferManager from "@/Components/fantasy/TransferManager/index";
import { Paginated } from "@/types/pagination";
import { useState, useCallback, useEffect } from "react";
import axios from "axios";  
import { useTranslation } from "react-i18next";

interface Player {
    id: number | string;
    name: string;
    position: "GK" | "DEF" | "MID" | "FWD";
    team: string;
    price: number;
    is_captain?: boolean;
    is_vice_captain?: boolean;
}

interface TransfersPageProps extends PageProps {
    competition: any;
    season: any;
    gameweek: any;
    fantasyTeam: any;
    currentSquad: any[];
    budget: number;
    freeTransfers: number;
    startingPlayerIds: number[];
}

export default function Transfers(props: TransfersPageProps) {
    const tenantName = useTenantName();
    const { i18n, t } = useTranslation();
    const { currentSquad, budget, freeTransfers } = props;

    // State for available players pagination (AJAX)
    const [availablePlayers, setAvailablePlayers] = useState<Paginated<any>>({
        data: [],
        current_page: 1,
        first_page_url: '',
        from: 0,
        last_page: 1,
        last_page_url: '',
        links: [],
        next_page_url: null,
        path: '',
        per_page: 20,
        prev_page_url: null,
        to: 0,
        total: 0,
    });
    const [isLoadingPlayers, setIsLoadingPlayers] = useState(false);

    // Function to fetch available players via AJAX
    const fetchAvailablePlayers = useCallback(async (filters: any = {}) => {
        console.log('fetchAvailablePlayers called with filters:', filters);
        setIsLoadingPlayers(true);
        try {
            console.log('Making AJAX request to /transfers/available-players');
            const response = await axios.get('/transfers/available-players', {
                params: {
                    ...filters,
                    locale: i18n.language,
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });
            console.log('AJAX response received:', response.data);
            setAvailablePlayers(response.data);
        } catch (error: any) {
            console.error('Failed to fetch available players:', error);
            console.error('Error details:', error.response?.data);
        } finally {
            setIsLoadingPlayers(false);
        }
    }, [i18n.language]);

    // Fetch available players when component mounts
    useEffect(() => {
        fetchAvailablePlayers();
    }, [fetchAvailablePlayers]);

    // Transform current squad data
    const transformedCurrentSquad: Player[] = currentSquad.map(
        (player: any) => ({
            id: player.id,
            name: player.name,
            position: player.position as "GK" | "DEF" | "MID" | "FWD",
            team: player.team || "Unknown",
            price: player.price || 5.0,
            is_captain: player.is_captain || false,
            is_vice_captain: player.is_vice_captain || false,
            onPitch: false, // Default value
            pitchPosition: null, // Default value
            team_data: player.team_data || { logo: '', shirt: { base_color: '', pattern_type: '', pattern_color: '' } },
        })
    );


    const handleSquadSubmit = (squadData: {
        players: Player[];
        captain: Player | null;
        viceCaptain: Player | null;
        budget: number;
    }) => {
        // Transform data for backend submission
        const selectedPlayers = squadData.players.map((player) => player.id);

        // For transfers, assume first 11 players are in lineup
        const lineupPlayers = squadData.players
            .slice(0, 11)
            .map((player) => player.id);

        // Calculate transfers made by comparing new squad with original squad
        const originalPlayerIds = currentSquad.map((p: any) => p.id);
        const newPlayerIds = squadData.players.map((p) => p.id);

        // Find players that are different between the two squads
        const transfersIn = newPlayerIds.filter(
            (id) => !originalPlayerIds.includes(id)
        );
        const transfersMade = transfersIn.length;

        const submissionData = {
            selected_players: selectedPlayers,
            lineup_players: lineupPlayers,
            captain_id: squadData.captain?.id || null,
            vice_captain_id: squadData.viceCaptain?.id || null,
            transfers_made: transfersMade,
        };

        console.log("Submitting transfer data:", submissionData);

        // Submit to backend
        router.post(route("game.transfers.process"), submissionData, {
            onSuccess: () => {
                console.log("Transfers submission successful");
                // Redirect to home or next step after successful transfers
                router.visit(route("game.team"));
            },
            onError: (errors) => {
                console.error("Transfers submission failed:", errors);
            },
        });
    };

    const { app, auth, tenant, competitions, currentCompetition, flash } = props;
    
    return (
        <Layout 
            app={app}
            auth={auth}
            tenant={tenant}
            competitions={competitions}
            currentCompetition={currentCompetition}
            flash={flash}
        >
            <Head title={`${tenantName} - Transfers`} />
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Header */}
                <HeroSection
                    title={t("transfers.title")}
                    subtitle={t("transfers.subtitle")}
                />
            </div>
            <TransferManager
                isCreationMode={false}
                initialSquadData={transformedCurrentSquad}
                availablePlayers={availablePlayers}
                initialBudget={budget}
                onSquadSubmit={handleSquadSubmit}
                onFetchPlayers={fetchAvailablePlayers}
                isLoadingPlayers={isLoadingPlayers}
            />
        </Layout>
    );
}
