import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { 
    TbTransfer, 
    TbChevronLeft,
    TbChevronRight
} from 'react-icons/tb';
import { useTranslation } from 'react-i18next';
import { Paginated } from '@/types/pagination';
import { useState } from 'react';
import axios from 'axios';
import { usePage } from '@inertiajs/react';
import { PageProps } from '@/types';

interface FantasyTransfer {
    id: number;
    fantasy_team_id: number;
    gameweek_id: number;
    player_in_id: number | null;
    player_out_id: number | null;
    transfer_cost: string;
    is_free_transfer: boolean;
    transfer_type: 'in' | 'out' | 'swap';
    tenant_id: number;
    created_at: string;
    updated_at: string;
    // Relations that might be loaded
    player_in?: {
        name: string;
        position: string;
        team: string;
        image: string;
    };
    player_out?: {
        name: string;
        position: string;
        team: string;
        image: string;
    };
    fantasy_team?: {
        name: string;
        user?: {
            name: string;
            avatar: string;
        };
    };
}

interface TransferNewsProps {
    transfers: Paginated<FantasyTransfer>;
    title?: string;
    selectedPhaseId?: string;
    selectedGameweekId?: string;
}

// Function to get styling based on is_free_transfer value
const getTransferStyling = (isFreeTransfer: boolean) => {
    if (isFreeTransfer) {
        return {
            bgColor: 'from-green-50 to-emerald-50',
            borderColor: 'border-green-200',
            textColor: 'text-green-800'
        };
    } else {
        return {
            bgColor: 'from-orange-50 to-amber-50',
            borderColor: 'border-orange-200',
            textColor: 'text-orange-800'
        };
    }
};

// Helper function to get player image URL
const getPlayerImageUrl = (playerImage: string | undefined, appUrl: string) => {
    if (!playerImage) return '';
    // Remove any leading slashes from the image path
    const cleanImagePath = playerImage.startsWith('/') ? playerImage.substring(1) : playerImage;
    return `${appUrl}/storage/${cleanImagePath}`;
};

// Helper function to get the main player for display (player_in takes priority)
const getDisplayPlayer = (transfer: FantasyTransfer) => {
    return transfer.player_in || transfer.player_out;
};

export default function LeagueNews({ transfers: initialTransfers, title, selectedPhaseId, selectedGameweekId }: TransferNewsProps) {
    const { t } = useTranslation();
    const { app } = usePage<PageProps>().props;
    const displayTitle = title || t('leagueNews.transferHighlights');
    
    // Local state for transfers data and loading
    const [transfers, setTransfers] = useState<Paginated<FantasyTransfer>>(initialTransfers);
    const [isLoading, setIsLoading] = useState(false);
    
    const handlePageChange = async (page: number) => {
        setIsLoading(true);
        try {
            // Extract league ID from current URL
            const pathParts = window.location.pathname.split('/');
            const leagueId = pathParts[pathParts.indexOf('leagues') + 1];
            
            // Make AJAX request for transfers only
            const response = await axios.get(`/leagues/${leagueId}/transfers`, {
                params: { 
                    page,
                    phase_id: selectedPhaseId,
                    gameweek_id: selectedGameweekId
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            // Update only the transfers state
            setTransfers(response.data);
        } catch (error) {
            console.error('Error loading transfers:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <TbTransfer className="w-5 h-5" /> {displayTitle}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-3">
                    {transfers.data.map((transfer: FantasyTransfer) => {
                        const cost = parseFloat(transfer.transfer_cost);
                        const styling = getTransferStyling(transfer.is_free_transfer);
                        const displayPlayer = getDisplayPlayer(transfer);
                        const teamName = transfer.fantasy_team?.name || transfer.fantasy_team?.user?.name || `Team ${transfer.fantasy_team_id}`;
                        
                        return (
                            <div key={transfer.id} className={`p-4 rounded-lg border bg-gradient-to-r ${styling.bgColor} ${styling.borderColor} hover:shadow-sm transition-shadow`}>
                                <div className="flex items-start gap-3">
                                    <div className="flex-shrink-0 mt-1">
                                        {displayPlayer?.image && displayPlayer.image.trim() !== '' ? (
                                            <div className="w-10 h-10 flex-shrink-0">
                                                <img
                                                    src={getPlayerImageUrl(displayPlayer.image, app.url)}
                                                    alt={`${displayPlayer.name} photo`}
                                                    className="w-full h-full object-cover rounded-full border-2 border-white shadow-sm"
                                                    onError={(e) => {
                                                        const target = e.target as HTMLImageElement;
                                                        target.src = `${app.url}/images/base-football-player.png`;
                                                        target.onerror = null;
                                                    }}
                                                />
                                            </div>
                                        ) : (
                                            <div className="w-10 h-10 flex-shrink-0">
                                                <img
                                                    src={`${app.url}/images/base-football-player.png`}
                                                    alt="Default player"
                                                    className="w-full h-full object-cover rounded-full border-2 border-white shadow-sm"
                                                />
                                            </div>
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between mb-2">
                                            <h4 className={`text-sm font-medium truncate ${styling.textColor}`}>
                                                {teamName}
                                            </h4>
                                            {transfer.fantasy_team?.user && (
                                                <span className="text-xs text-gray-600 ml-2">
                                                    {t('common.by', 'by')} {transfer.fantasy_team.user.name}
                                                </span>
                                            )}
                                        </div>
                                        
                                        <div className={`text-sm mb-2 ${styling.textColor}`}>
                                            {transfer.transfer_type === 'in' && transfer.player_in && (
                                                <span>
                                                    {transfer.is_free_transfer 
                                                        ? t('leagueNews.signedPlayerFree', 'Signed {{player}} (Free Transfer)', { player: transfer.player_in.name })
                                                        : t('leagueNews.signedPlayer', 'Signed {{player}}', { player: transfer.player_in.name })
                                                    } ({transfer.player_in.position}) from {transfer.player_in.team}
                                                </span>
                                            )}
                                            {transfer.transfer_type === 'out' && transfer.player_out && (
                                                <span>
                                                    {t('leagueNews.releasedPlayer', 'Released {{player}}', { player: transfer.player_out.name })} ({transfer.player_out.position}) from {transfer.player_out.team}
                                                </span>
                                            )}
                                            {transfer.transfer_type === 'swap' && transfer.player_in && transfer.player_out && (
                                                <span>
                                                    {transfer.is_free_transfer 
                                                        ? t('leagueNews.swappedPlayersFree', 'Swapped {{playerOut}} for {{playerIn}} (Free Transfer)', { playerOut: transfer.player_out.name, playerIn: transfer.player_in.name })
                                                        : t('leagueNews.swappedPlayers', 'Swapped {{playerOut}} for {{playerIn}}', { playerOut: transfer.player_out.name, playerIn: transfer.player_in.name })
                                                    }
                                                </span>
                                            )}
                                        </div>
                                        
                                        <div className="flex items-center gap-3 mt-3 text-xs text-gray-600">
                                            <span className="flex items-center gap-1">
                                                <span className="font-medium">{t('common.gameweek', 'GW')}:</span> {transfer.gameweek_id}
                                            </span>
                                            
                                            {transfer.is_free_transfer ? (
                                                <span className="flex items-center gap-1 text-green-700 font-medium">
                                                    {t('transfers.freeTransfer', 'Free Transfer')}
                                                </span>
                                            ) : (
                                                <span className="flex items-center gap-1 text-orange-700 font-medium">
                                                    {t('transfers.cost', 'Cost')}: ${transfer.transfer_cost}
                                                </span>
                                            )}
                                            
                                            <span className="text-gray-400">•</span>
                                            <span>{new Date(transfer.created_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}

                    {transfers.data.length === 0 && (
                        <div className="text-center py-8">
                            <TbTransfer className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                            <p className="text-sm text-muted-foreground">
                                {t('leagueNews.noTransfersMessage', 'No transfers made this gameweek. Teams are keeping faith in their squads!')}
                            </p>
                        </div>
                    )}
                </div>

                {/* Pagination Controls */}
                {transfers.last_page > 1 && (
                    <div className="flex items-center justify-between mt-6 pt-4 border-t">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(transfers.current_page - 1)}
                            disabled={transfers.current_page <= 1 || isLoading}
                            className="flex items-center gap-2"
                        >
                            <TbChevronLeft className="w-4 h-4" />
                            {t('pagination.previous', 'Previous')}
                        </Button>
                        
                        <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">
                                {t('pagination.page', 'Page')} {transfers.current_page} {t('pagination.of', 'of')} {transfers.last_page}
                            </span>
                        </div>
                        
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(transfers.current_page + 1)}
                            disabled={transfers.current_page >= transfers.last_page || isLoading}
                            className="flex items-center gap-2"
                        >
                            {isLoading ? (
                                <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
                            ) : (
                                <TbChevronRight className="w-4 h-4" />
                            )}
                            {t('pagination.next', 'Next')}
                        </Button>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}